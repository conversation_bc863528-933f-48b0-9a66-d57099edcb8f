{"name": "LiLi-IM", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.4", "babel-plugin-prismjs": "^2.0.1", "core-js": "^3.6.5", "cors": "^2.8.5", "element-ui": "^2.14.1", "js-audio-recorder": "^1.0.6", "js-base64": "^2.5.1", "mavon-editor": "^2.10.4", "nprogress": "^0.2.0", "prismjs": "^1.29.0", "svg-sprite-loader": "^5.0.0", "vue": "^2.6.11", "vue-contextmenujs": "^1.3.13", "vue-cropper": "^0.5.5", "vue-prism-editor": "^0.5.1", "vue-router": "^3.4.9", "vuex": "^3.5.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-service": "^5.0.8", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.1", "compression-webpack-plugin": "^5.0.0", "eslint": "^8.28.0", "eslint-plugin-vue": "^6.2.2", "file-loader": "^6.2.0", "less": "^3.0.4", "less-loader": "^5.0.0", "postcss": "^8.4.20", "style-resources-loader": "^1.4.1", "vue-cli-plugin-style-resources-loader": "^0.1.4", "vue-svg-component-runtime": "^1.0.1", "vue-svg-icon-loader": "^2.1.1", "vue-template-compiler": "^2.6.11", "webpack": "^5.75.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}