<template>
  <div>
    <el-container class="editor-container">
      <el-header class="no-padding toolbar" height="35px">
        <ul>
          <li v-popover:popoverEmoticon>
            <i class="iconfont icon-icon_im_face" style="font-size: 15px" />
            <p class="tip-title">表情符号</p>
          </li>
          <!-- <li @click="codeBlock.isShow = true">
            <i class="iconfont icon-daima" />
            <p class="tip-title">代码片段</p>
          </li>
          <li @click="recorder = true">
            <i class="el-icon-headset" />
            <p class="tip-title">语音消息</p>
          </li> -->
          <!-- #TODO 发图片功能暂时隐藏 此处会涉及到token过期 -->
           <li @click="$refs.restFile.click()">
            <i class="el-icon-picture-outline-round" />
            <p class="tip-title">图片</p>
          </li>

          <!-- ★★★ 添加或取消注释文件按钮 ★★★ -->
          <li @click="$refs.restFile2.click()">
            <i class="el-icon-folder" />
            <p class="tip-title">文件</p>
          </li>

          <!-- <li v-show="isGroupTalk" @click="vote.isShow = true">
            <i class="el-icon-s-data" />
            <p class="tip-title">发起投票</p>
          </li> -->

          <!--          <p class="text-tips no-select">-->
          <!--            <span>按Enter发送 / Shift+Enter 换行</span>-->
          <!--            <el-popover placement="top-end" width="600" trigger="click">-->
          <!--              <div class="editor-books">-->
          <!--                <div class="books-title">编辑说明:</div>-->
          <!--                <p>-->
          <!--                  1.-->
          <!--                  支持上传QQ及微信截图，在QQ或微信中截图后使用Ctrl+v上传图片。-->
          <!--                </p>-->
          <!--                <p>-->
          <!--                  2.-->
          <!--                  支持浏览器及Word文档中的图片复制上传、复制后使用Ctrl+v上传图片。-->
          <!--                </p>-->
          <!--                <p>3. 支持图片拖拽上传。</p>-->
          <!--                <p>4. 支持文件上传 ( 文件小于100M ) 。</p>-->
          <!--                <p>5. 按Enter发送 / Shift+Enter 换行。</p>-->
          <!--                <p>-->
          <!--                  6.-->
          <!--                  注意：当文件正在上传时，请勿关闭网页或离开当前对话框，否则将导致文件停止上传或上传失败。-->
          <!--                </p>-->
          <!--              </div>-->
          <!--              <i class="el-icon-info" slot="reference" />-->
          <!--            </el-popover>-->
          <!--          </p>-->
        </ul>

        <el-popover ref="popoverEmoticon" placement="top-start" trigger="click" width="300"
          popper-class="no-padding el-popover-em">
          <MeEditorEmoticon ref="editorEmoticon" @selected="selecteEmoticon" />
        </el-popover>

        <form enctype="multipart/form-data" style="display: none" ref="fileFrom">
          <input type="file" ref="restFile" accept="image/*" @change="uploadImageChange" />
          <input type="file" ref="restFile2" @change="uploadFileChange" />
        </form>
      </el-header>
      <el-main class="no-padding editor-main" style="position: relative;">
        <textarea
          ref="textarea"
          v-model="editorText"
          class="textarea"
          placeholder="你想要的聊点什么呢 ..."
          @keydown="keydownEvent($event)"
          @paste="pasteImage($event)"
          @drop="dragPasteImage($event)"
          @input="inputEvent($event)"
        />

        <el-button
          type="primary"
          size="mini"
          class="send-button"
          @click="clickSendMessage"
        >
          发送
        </el-button>
      </el-main>
    </el-container>

    <!-- 图片查看器 -->
    <MeEditorImageView ref="imageViewer" v-model="imageViewer.isShow" :file="imageViewer.file"
      @confirm="confirmUploadImage" />

    <MeEditorRecorder v-if="recorder" @close="recorder = false" />

    <!-- 代码块编辑器 -->
    <TalkCodeBlock v-if="codeBlock.isShow" :edit-mode="codeBlock.editMode" @close="codeBlock.isShow = false"
      @confirm="confirmCodeBlock" />

    <!-- 文件上传管理器 -->
    <MeEditorFileManage ref="filesManager" v-model="filesManager.isShow" @upload-success="handleFileUploadSuccess" />

    <MeEditorVote v-if="vote.isShow" @close="
      () => {
        this.vote.isShow = false;
      }
    " />
  </div>
</template>

<script>
import MeEditorEmoticon from "./MeEditorEmoticon";
import MeEditorFileManage from "./MeEditorFileManage";
import MeEditorImageView from "./MeEditorImageView";
import MeEditorRecorder from "./MeEditorRecorder";
import MeEditorVote from "./MeEditorVote";
import TalkCodeBlock from "@/components/chat/TalkCodeBlock";
import { getPasteImgs, getDragPasteImg } from "@/utils/editor";
import { findTalk } from "@/utils/talk";

import {
  ServeSendTalkCodeBlock,
  ServeSendTalkImage,
  ServeSendEmoticon,
  // ★★★ 确保导入了发送文件的 API (如果 MeEditor 直接发送的话，但这里是由 TalkPanel 发送) ★★★
  // ServeSendTalkFile, // 通常上传在 FileManage 完成，这里只需要传递信息
} from "@/api/chat";

export default {
  name: "MeEditor",
  components: {
    MeEditorEmoticon,
    MeEditorFileManage,
    MeEditorImageView,
    TalkCodeBlock,
    MeEditorRecorder,
    MeEditorVote,
  },
  computed: {
    talkUser () {
      return this.$store.state.dialogue.index_name;
    },
    isGroupTalk () {
      return this.$store.state.dialogue.talk_type == 2;
    },
  },
  watch: {
    talkUser (n_index_name) {
      this.$refs.filesManager.clear();
      this.editorText = this.getDraftText(n_index_name);
    },
  },
  data () {
    return {
      // 当前编辑的内容
      editorText: "",

      // 图片查看器相关信息
      imageViewer: {
        isShow: false,
        file: null,
      },

      codeBlock: {
        isShow: false,
        editMode: true,
      },

      filesManager: {
        isShow: false,
      },

      vote: {
        isShow: false,
      },

      // 录音器
      recorder: false,

      // 上次发送消息的时间
      sendtime: 0,

      // 发送间隔时间（毫秒）
      interval: 1000,
    };
  },
  methods: {
    // 读取对话编辑草稿信息 并赋值给当前富文本
    getDraftText (index_name) {
      return findTalk(index_name)?.draft_text || "";
    },

    //复制粘贴图片回调方法
    pasteImage (e) {
      let files = getPasteImgs(e);
      if (files.length == 0) return;

      this.openImageViewer(files[0]);
    },

    //拖拽上传图片回调方法
    dragPasteImage (e) {
      let files = getDragPasteImg(e);
      if (files.length == 0) return;

      this.openImageViewer(files[0]);
    },

    inputEvent (e) {
      this.$emit("keyboard-event", e.target.value);
    },

    // 键盘按下监听事件
    keydownEvent (e) {
      if (e.keyCode == 13 && this.editorText == "") {
        e.preventDefault();
      }

      // 回车发送消息
      if (e.keyCode == 13 && e.shiftKey == false && this.editorText != "") {
        this.triggerSend();
        e.preventDefault();
      }
    },

    // 选择图片文件后回调方法
    uploadImageChange (e) {
      this.openImageViewer(e.target.files[0]);
      this.$refs.restFile.value = null;
    },

    // 选择文件回调事件
    uploadFileChange (e) {
      let maxsize = 100 * 1024 * 1024; // 100MB 大小限制
      if (e.target.files.length == 0) {
        return false;
      }

      let file = e.target.files[0];

      // 1. 如果是图片，走图片预览流程
      if (/\.(gif|jpg|jpeg|png|webp|GIF|JPG|PNG|WEBP)$/.test(file.name)) {
        this.openImageViewer(file); // 打开图片预览，确认后由 confirmUploadImage 处理上传和发送
        this.$refs.restFile2.value = null; // 清空文件选择，以便下次选择同名文件
        return;
      }

      // 2. 检查文件大小
      if (file.size > maxsize) {
        this.$notify.info({
          title: "消息",
          message: "上传文件不能大于100M",
        });
        this.$refs.restFile2.value = null; // 清空
        return;
      }

      // 3. 对于非图片文件，打开文件管理器并开始上传
      this.filesManager.isShow = true; // 显示文件管理器 UI
      this.$refs.restFile2.value = null; // 清空
      // 调用文件管理器组件的 upload 方法来处理上传过程
      // 上传成功后会触发 @upload-success 事件，由 handleFileUploadSuccess 处理
      this.$refs.filesManager.upload(file);
    },

    // 打开图片查看器
    openImageViewer (file) {
      this.imageViewer.file = file;
      this.imageViewer.isShow = true;
      // 清空 restFile 的值，允许再次选择相同文件
      if (this.$refs.restFile) {
        this.$refs.restFile.value = null;
      }
      // 也清空 restFile2，因为 uploadFileChange 可能也调用了它
      if (this.$refs.restFile2) {
        this.$refs.restFile2.value = null;
      }
    },

    // 代码块编辑器确认完成回调事件
    confirmCodeBlock (data) {
      const { talk_type, receiver_id } = this.$store.state.dialogue;
      ServeSendTalkCodeBlock({
        talk_type,
        receiver_id,
        code: data.code,
        lang: data.language,
      }).then((res) => {
        if (res.code == 200) {
          this.codeBlock.isShow = false;
        } else {
          this.$notify({
            title: "友情提示",
            message: res.message,
            type: "warning",
          });
        }
      });
    },

    // 确认上传图片消息回调事件
    confirmUploadImage () {
      let fileData = new FormData();
      fileData.append("file", this.imageViewer.file);

      let ref = this.$refs.imageViewer;
      ref.loading = true;

      ServeSendTalkImage(fileData)
        .then((res) => {
          if (res.code == 200 && res.result && typeof res.result === 'string') {
            const imageUrl = res.result;
            // 直接触发 send-image 事件给 TalkPanel
            this.$emit('send-image', imageUrl);
            this.imageViewer.isShow = false;
          } else {
            this.$notify({
              title: "友情提示",
              message: res.code !== 200 ? (res.message || '图片上传失败') : '图片上传成功，但响应格式错误',
              type: "warning",
            });
          }
        })
        .catch(err => {
           console.error("Upload image error:", err);
           this.$notify({
              title: "网络错误",
              message: '图片上传异常',
              type: "error",
            });
        })
        .finally(() => {
          if(ref) {
             ref.loading = false;
          }
        });
    },

    // 选中表情包回调事件
    selecteEmoticon (data) {
      // --- 添加日志 ---
      console.log('selecteEmoticon called with data:', data);
      console.log('data.type:', data.type);
      console.log('data.value:', data.value);
      // --- 日志结束 ---

      if (data.type == 1) {
        // --- 添加日志 ---
        console.log('Handling as text emoji (type 1)');
        // --- 日志结束 ---
        let value = this.editorText;
        let el = this.$refs.textarea;
        let startPos = el.selectionStart;
        let endPos = el.selectionEnd;
        let newValue =
          value.substring(0, startPos) +
          data.value +
          value.substring(endPos, value.length);

        this.editorText = newValue;

        if (el.setSelectionRange) {
          setTimeout(() => {
            let index = startPos + data.value.length;
            el.setSelectionRange(index, index);
            el.focus();
          }, 0);
        }
      } else {
        // --- 添加日志 ---
        console.log('Handling as image emoticon (type != 1)');
        // --- 日志结束 ---
        const { talk_type, receiver_id } = this.$store.state.dialogue;

        // --- 添加日志 ---
        console.log('Preparing to call ServeSendEmoticon with:', {
          talk_type,
          receiver_id,
          emoticon_id: data.value,
        });
        // --- 日志结束 ---

        ServeSendEmoticon({
          talk_type,
          receiver_id,
          emoticon_id: data.value,
        }).then(res => { // 添加 .then 处理，看看 API 调用结果
            console.log('ServeSendEmoticon response:', res);
        }).catch(err => { // 添加 .catch 处理，捕获错误
            console.error('ServeSendEmoticon error:', err);
        });
      }

      this.$refs.popoverEmoticon.doClose();
    },

    // 添加一个方法来触发文件选择，可以被外部按钮调用
    triggerUploadImage() {
      this.$refs.restFile.click();
    },

    // ★★★ 新增：点击按钮发送消息的方法 ★★★
    clickSendMessage() {
      this.triggerSend();
    },

    // ★★★ 新增：触发发送的核心逻辑（供按键和按钮调用）★★★
    triggerSend() {
      if (this.editorText.trim() === "") {
        // 可以选择性地提示用户不能发送空消息
        // this.$message.warning("不能发送空消息");
        return;
      }

      let currentTime = new Date().getTime();
      if (this.sendtime > 0) {
        // 判断 1秒内只能发送一条消息
        if (currentTime - this.sendtime < this.interval) {
          // 可以选择性地提示用户发送过于频繁
          // this.$message.warning("消息发送过于频繁");
          return false;
        }
      }

      // 触发 send 事件，将文本传递给父组件 (TalkPanel.vue)
      this.$emit("send", this.editorText);

      // 清空输入框
      this.editorText = "";

      // 更新发送时间戳
      this.sendtime = currentTime;

      // 让输入框重新获得焦点（可选）
      this.$nextTick(() => {
        this.$refs.textarea.focus();
      });
    },

    // ★★★ 新增：处理文件上传成功的回调 ★★★
    handleFileUploadSuccess(fileInfo) {
      // fileInfo 应该包含 { name: 'xxx.pdf', url: '...', size: 12345 } 等信息
      console.log('File uploaded successfully in FileManage, emitting send-file:', fileInfo);
      // 触发 send-file 事件，将文件信息传递给父组件 TalkPanel
      this.$emit('send-file', fileInfo);

      // 可选：上传成功后关闭文件管理器
      // this.filesManager.isShow = false;
    },
  },
};
</script>
<style scoped lang="less">
.editor-container {
  height: 160px;
  width: 100%;
  background-color: white;
}

.editor-container .toolbar {
  line-height: 35px;
  border-bottom: 1px solid #f5f0f0;
  border-top: 1px solid #f5f0f0;
}

.editor-container .toolbar li {
  list-style: none;
  float: left;
  width: 35px;
  margin-left: 3px;
  cursor: pointer;
  text-align: center;
  line-height: 35px;
  position: relative;
  color: #8d8d8d;
}

.editor-container .toolbar li .tip-title {
  display: none;
  position: absolute;
  top: 38px;
  left: 0px;
  height: 26px;
  line-height: 26px;
  background-color: rgba(31, 35, 41, 0.9);
  color: white;
  min-width: 30px;
  font-size: 10px;
  padding: 0 5px;
  border-radius: 2px;
  white-space: pre;
  text-align: center;
  user-select: none;
  z-index: 1;
}

.editor-container .toolbar li:hover .tip-title {
  display: block;
}

.editor-container .toolbar li:hover {
  background-color: #f7f5f5;
}

.editor-container .toolbar .text-tips {
  float: right;
  margin-right: 15px;
  font-size: 12px;
  color: #ccc;
}

.editor-container .toolbar .text-tips i {
  font-size: 14px;
  cursor: pointer;
  margin-left: 5px;
  color: rgb(255, 181, 111);
}

.editor-container .editor-main {
  position: relative;
  overflow: hidden;
}

.editor-container .textarea {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 10px;
  padding-bottom: 40px;
  resize: none;
  background-color: #fff;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  overflow-y: auto;
}

textarea::-webkit-scrollbar {
  width: 4px;
  height: 1px;
}

textarea::-webkit-scrollbar-thumb {
  background: #d5cfcf;
}

textarea::-webkit-scrollbar-track {
  background: #ededed;
}

textarea::-webkit-input-placeholder {
  color: #dccdcd;
  font-size: 12px;
  font-weight: 400;
}

/* 编辑器文档说明 --- start */
.editor-books .books-title {
  font-size: 16px;
  height: 30px;
  line-height: 22px;
  margin-top: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #cbcbcb;
  color: #726f6f;
  font-weight: 400;
  margin-left: 11px;
}

.editor-books p {
  text-indent: 10px;
  font-size: 12px;
  height: 30px;
  line-height: 30px;
  color: #7f7c7c;
}

/* 编辑器文档说明 --- end */

.send-button {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 10;
}
</style>
