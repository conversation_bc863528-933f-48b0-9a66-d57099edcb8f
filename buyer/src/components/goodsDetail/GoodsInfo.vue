<template>
  <div class="goods-info-container">
    <!-- 商品标题 -->
    <div class="goods-title">
      <h1>{{ skuInfo.goodsName || '商品加载中...' }}</h1>
      <!-- 可以加副标题或标签 -->
      <Tag v-if="skuInfo.goodsType && skuInfo.goodsType !== 'VIRTUAL_GOODS'" color="default">实物商品</Tag>
      <Tag v-else-if="skuInfo.goodsType === 'VIRTUAL_GOODS'" color="blue">虚拟商品</Tag>
    </div>

    <!-- 卖点 -->
    <div v-if="skuInfo.sellingPoint" class="selling-point">
      {{ skuInfo.sellingPoint }}
    </div>

    <!-- 价格区域 -->
    <div class="price-section">
        <!-- 秒杀进行中 -->
         <div v-if="promotions.SECKILL" class="seckill-active">
             <div class="seckill-tag">限时秒杀</div>
             <div class="price-line">
                 <span class="label">秒杀价</span>
                 <span class="current-price price">{{ skuInfo.promotionPrice | unitPrice('￥') }}</span>
                 <span class="original-price price">{{ skuInfo.price | unitPrice('￥') }}</span>
             </div>
              <!-- TODO: 可以添加倒计时 -->
             <PromotionCountdown :endTime="promotions.SECKILL.endTime" />
         </div>

        <!-- 普通/批发价格 -->
         <div v-else class="normal-price">
             <!-- 批发价格 -->
            <div v-if="computedWholesaleList.length">
                 <div v-for="(item, index) in computedWholesaleList" :key="index" class="wholesale-price-line">
                     <span class="label">{{ index === 0 ? '批发价' : '' }}</span>
                     <span class="price">{{ item.price | unitPrice('￥') }}</span>
                     <span class="condition"> (≥ {{ item.num }}{{ skuInfo.goodsUnit || '件' }})</span>
                 </div>
            </div>
             <!-- 普通价格 -->
             <div v-else class="price-line">
                <span class="label">价格</span>
                <span class="current-price price">{{ skuInfo.price | unitPrice('￥') }}</span>
             </div>
         </div>
         <!-- 累计评价 -->
         <div class="comment-summary">
           <span class="label">累计评价</span>
           <span class="count">{{ skuInfo.commentNum || 0 }}</span>
         </div>
    </div>

    <!-- 促销信息 -->
    <div class="promotion-section">
      <!-- 优惠券 -->
      <div v-if="promotions.COUPON && promotions.COUPON.length" class="promo-item coupon-item">
        <span class="label">优惠券</span>
        <div class="promo-content">
          <span
            class="coupon-tag"
            v-for="(coupon, index) in promotions.COUPON.slice(0, 3)" 
            :key="coupon.id || index"
            @click="handleReceiveCoupon(coupon.id)"
           >
            <span v-if="coupon.couponType === 'PRICE'">满{{ coupon.consumeThreshold }}减{{ coupon.price }}</span>
            <span v-if="coupon.couponType === 'DISCOUNT'">满{{ coupon.consumeThreshold }}打{{ coupon.couponDiscount }}折</span>
          </span>
          <!-- TODO: 更多优惠券 -->
           <span v-if="promotions.COUPON.length > 3" class="more-coupons">更多</span>
        </div>
      </div>
      <!-- 满减 -->
       <div v-if="promotions.FULL_DISCOUNT" class="promo-item full-discount-item">
        <span class="label">促&nbsp;&nbsp;&nbsp;销</span>
         <div class="promo-content">
            <span class="promo-tag">满减</span>
            <span class="promo-desc">
                 <span v-if="promotions.FULL_DISCOUNT.fullMinus">满{{ promotions.FULL_DISCOUNT.fullMoney }}元，立减{{ promotions.FULL_DISCOUNT.fullMinus }}元</span>
                 <span v-if="promotions.FULL_DISCOUNT.fullRate && promotions.FULL_DISCOUNT.fullRateFlag"> 满{{ promotions.FULL_DISCOUNT.fullMoney }}元，立享{{ promotions.FULL_DISCOUNT.fullRate }}折</span>
                 <!-- TODO: 可能还有赠品等其他满减类型 -->
             </span>
         </div>
      </div>
       <!-- TODO: 其他促销类型，如赠品、积分等 -->
    </div>

     <!-- 规格选择 -->
    <div class="sku-selection-section">
      <div v-for="(spec, specIndex) in specList" :key="spec.name || specIndex" class="spec-group">
        <div class="spec-name">{{ spec.name }}</div>
        <div class="spec-values">
          <span
            v-for="(val, valIndex) in spec.values"
            :key="val.value || valIndex"
            class="spec-value-item"
            :class="{
              active: currentSelection[specIndex] === val.value,
              disabled: val.disabled // TODO: 实现规格可选性判断
            }"
            @click="selectSku(specIndex, val.value, val.disabled)"
          >
            <!-- 可以根据类型显示图片或文字 -->
             <!-- <img v-if="val.image" :src="val.image" :alt="val.value"> -->
            {{ val.value }}
          </span>
        </div>
      </div>
    </div>

    <!-- 重量显示 (如果需要且独立于购买操作区) -->
     <div class="spec-group weight-display-section" v-if="skuInfo.weight && skuInfo.goodsType !== 'VIRTUAL_GOODS'">
         <div class="spec-name">重量</div>
         <div class="weight-info">{{ skuInfo.weight }} kg</div>
     </div>

    <!-- 购买操作区域 (合并数量和按钮) -->
    <div class="purchase-controls">
        <!-- 数量选择 -->
        <div class="quantity-selector">
            <div class="spec-name">数量</div>
            <div class="quantity-inputs">
                <InputNumber
                    :min="minBuyNum"
                    :max="skuInfo.quantity"
                    :step="1"
                    :disabled="!isSelectedSkuValid || skuInfo.quantity === 0"
                    v-model="currentCount"
                    @on-change="onCountChange"
                    @on-blur="onCountBlur"
                 ></InputNumber>
                 <span class="stock-info">(库存 {{ skuInfo.quantity || 0 }} {{ skuInfo.goodsUnit || '件' }})</span>
                 <span v-if="isWholesale && currentCount < minBuyNum" class="wholesale-warning">
                     起批量 {{ minBuyNum }} {{ skuInfo.goodsUnit || '件' }}
                 </span>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons-group">
            <!-- 积分购买按钮 -->
            <template v-if="isPointGoods && skuInfo.authFlag === 'PASS'">
                 <Button
                    type="error" 
                    size="large"
                    :loading="actionLoading"
                    :disabled="!isSelectedSkuValid || skuInfo.quantity === 0 || currentCount < minBuyNum"
                    @click="handlePointPay"
                  > 积分购买 </Button>
            </template>
            <!-- 普通购买按钮 -->
            <template v-else-if="skuInfo.authFlag === 'PASS'">
                 <Button
                   v-if="skuInfo.goodsType !== 'VIRTUAL_GOODS'"
                   type="warning" 
                   size="large"
                   ghost
                   icon="ios-cart-outline"
                   :loading="cartLoading"
                   :disabled="!isSelectedSkuValid || skuInfo.quantity === 0 || currentCount < minBuyNum"
                   @click="handleAddToCart"
                 > 加入购物车 </Button>
                 <Button
                   type="error" 
                   size="large"
                   :loading="buyLoading"
                   :disabled="!isSelectedSkuValid || skuInfo.quantity === 0 || currentCount < minBuyNum"
                   @click="handleBuyNow"
                 > 立即购买 </Button>
            </template>
            <!-- 商品下架等 -->
            <template v-else>
                <Button type="default" size="large" disabled>{{ skuInfo.authFlag === 'TOBEAUDITED' ? '审核中' : '商品已下架' }}</Button>
            </template>
        </div>
         <!-- 收藏按钮 (放在购买区右侧) -->
         <div class="collect-button-wrapper">
             <div class="collect-button" @click="handleCollect">
                 <Icon type="ios-heart" :color="isCollected ? '#ed3f14' : '#999'" size="18" />
                 <span>{{ isCollected ? '已收藏' : '收藏' }}</span>
             </div>
         </div>
    </div>

     <!-- 服务承诺/说明 -->
     <div class="service-commitment">
         <!-- TODO: 从接口获取或配置服务说明 -->
         <span>· 7天无理由退货</span>
         <span>· 正品保障</span>
         <span>· 急速退款</span>
     </div>
  </div>
</template>

<script>
import PromotionCountdown from "./Promotion.vue"; // 引入倒计时组件
import { receiveCoupon } from "@/api/member.js"; // 引入领取优惠券API

export default {
  name: "GoodsInfo",
  components: { PromotionCountdown },
  props: {
    skuInfo: { // 当前 SKU 的详细信息
      type: Object,
      required: true,
      default: () => ({})
    },
    specList: { // 格式化后的规格列表，用于显示
      type: Array,
      default: () => []
    },
    currentSelection: { // 父组件维护的当前选中规格数组
      type: Array,
      default: () => []
    },
    promotions: { // 促销信息对象 {SECKILL, FULL_DISCOUNT, COUPON[]}
      type: Object,
      default: () => ({ SECKILL: null, FULL_DISCOUNT: null, COUPON: [] })
    },
    wholesaleList: { // 批发价格列表
      type: Array,
      default: () => []
    },
    isPointGoods: { // 是否为积分商品页面
      type: Boolean,
      default: false
    },
    isCollected: { // 是否已收藏
      type: Boolean,
      default: false
    },
     cartLoading: { // 加入购物车按钮加载状态
       type: Boolean,
       default: false
     },
     buyLoading: { // 立即购买按钮加载状态
       type: Boolean,
       default: false
     },
      actionLoading: { // 其他动作按钮加载状态 (如积分购买)
        type: Boolean,
        default: false
      }
  },
  data() {
    return {
      currentCount: 1, // 本地维护的数量状态
    };
  },
  computed: {
    // 格式化批发数据以便显示
    computedWholesaleList() {
        if (!this.wholesaleList || this.wholesaleList.length === 0) {
            return [];
        }
         // 按数量升序排序
        return [...this.wholesaleList].sort((a, b) => a.num - b.num);
    },
    // 是否为批发购买
    isWholesale() {
        return this.computedWholesaleList.length > 0;
    },
    // 最小购买数量 (批发或普通)
    minBuyNum() {
        return this.isWholesale ? this.computedWholesaleList[0].num : 1;
    },
     // 判断当前选中的SKU是否完整且有效 (库存 > 0)
     isSelectedSkuValid() {
        // 1. 检查规格是否都选了
        const isFullySelected = this.specList.length === 0 || 
                               (this.currentSelection.length === this.specList.length && 
                                this.currentSelection.every(val => val !== undefined && val !== null));
        // 2. 检查库存
        const hasStock = this.skuInfo.quantity > 0;
        
        return isFullySelected && hasStock;
     }
  },
  watch: {
    // 当父组件的 wholesaleList 或 skuInfo 变化时，重置本地数量
    wholesaleList: {
      immediate: true,
      handler() {
        this.resetCount();
      }
    },
    'skuInfo.id': { // 监听 skuId 变化来重置数量
       handler() {
          this.resetCount();
       }
    },
    // Watcher to reset count when sku changes
    skuInfo: {
      handler(newSku, oldSku) {
        if (newSku && oldSku && newSku.id !== oldSku.id) {
          this.currentCount = 1;
        }
      },
      deep: true // Watch nested properties if needed
    }
  },
  methods: {
    // 重置数量为最小购买量
    resetCount(){
       this.currentCount = this.minBuyNum;
       this.$emit('changeCount', this.currentCount);
    },
    // 处理规格选择点击
    selectSku(specIndex, value, disabled) {
      if (disabled) {
        this.$Message.warning("该规格暂不可选");
        return;
      }
      // 如果点击的是当前已选中的，则不作处理 (或允许取消选择？)
      // if (this.currentSelection[specIndex] === value) return;
      
      this.$emit("selectSku", { specIndex, value });
    },
    // 数量变化时触发 (InputNumber change)
    onCountChange(newCount) {
       // 确保数量不低于最小起批量
       if (this.isWholesale && newCount < this.minBuyNum) {
          // 修正数量，但 InputNumber 可能自己处理了 min，这里可以只发事件
          // this.currentCount = this.minBuyNum;
          // this.$Message.warning(`起批量为 ${this.minBuyNum}`);
       }
      this.$emit("changeCount", newCount);
    },
    // 数量输入框失焦时触发 (用于最终校验)
    onCountBlur() {
        if (this.isWholesale && this.currentCount < this.minBuyNum) {
            this.currentCount = this.minBuyNum; 
            this.$Message.warning(`购买数量不能小于起批量 ${this.minBuyNum}`);
            this.$emit("changeCount", this.currentCount);
        }
        // 也可以在这里校验是否超过库存，但 InputNumber 的 max 应该已经处理
        // if (this.currentCount > this.skuInfo.quantity) {
        //      this.currentCount = this.skuInfo.quantity;
        //      this.$Message.warning(`最多只能购买 ${this.skuInfo.quantity} 件`);
        //      this.$emit("changeCount", this.currentCount);
        // }
    },
    // 处理领取优惠券
    handleReceiveCoupon(couponId) {
        if (!couponId) return;
        // 检查登录状态
        if (!this.Cookies.getItem("userInfo")) {
            this.$Message.warning("请先登录");
            // 可以跳转登录页
            return;
        }

        receiveCoupon(couponId).then(res => {
            if (res.success) {
                this.$Message.success("优惠券领取成功！");
                // 通知父组件，可能需要刷新促销信息或优惠券列表状态
                this.$emit('couponReceived', couponId);
            } else {
                this.$Message.warning(res.message || "领取失败，请稍后再试");
            }
        }).catch(err => {
            console.error("Error receiving coupon:", err);
            this.$Message.error("网络错误，领取失败");
        });
    },
    // 处理加入购物车
    handleAddToCart() {
        if (!this.isSelectedSkuValid) {
             this.$Message.warning("请选择完整的规格或商品暂无库存");
             return;
        }
         if (this.isWholesale && this.currentCount < this.minBuyNum) {
             this.$Message.warning(`购买数量不能小于起批量 ${this.minBuyNum}`);
             return;
         }
      this.$emit("addToCart", this.currentCount);
    },
    // 处理立即购买
    handleBuyNow() {
        if (!this.isSelectedSkuValid) {
            this.$Message.warning("请选择完整的规格或商品暂无库存");
            return;
        }
        if (this.isWholesale && this.currentCount < this.minBuyNum) {
             this.$Message.warning(`购买数量不能小于起批量 ${this.minBuyNum}`);
             return;
         }
      this.$emit("buyNow", this.currentCount);
    },
     // 处理积分购买
    handlePointPay() {
         if (!this.isSelectedSkuValid) {
             this.$Message.warning("请选择完整的规格或商品暂无库存");
             return;
         }
         if (this.isWholesale && this.currentCount < this.minBuyNum) {
             this.$Message.warning(`购买数量不能小于起批量 ${this.minBuyNum}`);
             return;
         }
        this.$emit("pointPay", this.currentCount);
    },
    // 处理收藏点击
    handleCollect() {
      this.$emit("collect");
    },
  },
  mounted() {
    this.resetCount();
  },
  beforeDestroy() {
  }
};
</script>

<style scoped lang="scss">
.goods-info-container {
  flex: 1; /*占据剩余空间*/
  display: flex;
  flex-direction: column;
  padding-left: 20px; /* 与图片区域保持距离 */
}

.goods-title {
  margin-bottom: 8px;
  h1 {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    line-height: 1.4;
    margin-bottom: 4px;
    word-break: break-all; /* 长标题换行 */
  }
  .ivu-tag {
    margin-left: 5px;
    vertical-align: middle;
  }
}

.selling-point {
  color: $theme_color; /* 使用主题色 */
  font-size: 12px;
  margin-bottom: 10px;
  line-height: 1.5;
}

/* Price Section Styles */
.price-section {
  background-color: #f9f9f9; /* Slightly lighter grey */
  padding: 20px; /* Increased padding */
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Keep top alignment */
  border-radius: 4px;

  .price-line, .wholesale-price-line, .seckill-active .price-line {
    display: flex;
    align-items: baseline;
    margin-bottom: 8px; /* Increased spacing */
    line-height: 1.2; /* Adjust line height slightly */
  }
  
  .seckill-active {
      flex-grow: 1;
      margin-right: 15px; /* Add margin to separate from comments */
      .seckill-tag {
          background-color: $theme_color;
          color: #fff;
          padding: 3px 8px; /* Larger tag padding */
          font-size: 12px;
          font-weight: bold;
          border-radius: 2px;
          margin-bottom: 10px; /* More space below tag */
          display: inline-block;
      }
       /* Style for countdown timer if needed */
       .promotion-countdown {
         margin-top: 5px;
       }
  }
  .normal-price {
      flex-grow: 1;
       margin-right: 15px; /* Add margin to separate from comments */
  }

  .label {
    color: #666; /* Darker label */
    font-size: 13px;
    width: 60px; /* Slightly wider label */
    margin-right: 10px;
    text-align: right; /* Align label text right */
  }

  .price {
    font-family: Verdana, Arial, sans-serif; /* Common price fonts */
    font-weight: bold;
    color: $theme_color;
    margin-right: 8px;
  }
  .current-price {
     font-size: 24px; /* Larger price */
  }
  .original-price {
     font-size: 14px;
     color: #999;
     text-decoration: line-through;
     margin-left: 5px; /* Space from current price */
  }
   .wholesale-price-line .price {
      font-size: 20px; /* Larger wholesale price */
   }
   .wholesale-price-line .condition {
       font-size: 12px;
       color: #666;
       margin-left: 5px; /* Space from price */
   }

  .comment-summary {
    text-align: center; /* Center align comment count */
    border-left: 1px solid #e5e5e5; /* Slightly darker border */
    padding-left: 20px; /* Increased padding */
    margin-left: 20px; /* Increased margin */
    min-width: 80px;
    flex-shrink: 0; /* Prevent shrinking */
    padding-top: 5px; /* Align top better with price */
    .label {
      display: block;
      color: #666;
      font-size: 12px;
      margin-bottom: 8px; /* More space */
      width: auto;
      text-align: center;
    }
    .count {
      color: #005ea7; /* JD link blue */
      font-size: 16px; /* Larger count */
      font-weight: bold;
       cursor: pointer;
       &:hover {
         color: $theme_color;
       }
    }
  }
}

/* Promotion Section Styles */
.promotion-section {
  margin-bottom: 20px; /* More space below */
  font-size: 12px;
  padding-left: 10px; /* Indent content slightly */

  .promo-item {
    display: flex;
    align-items: baseline; /* Align baseline */
    margin-bottom: 10px; /* Increased spacing */
  }

  .label {
    color: #666;
    font-size: 13px;
    width: 60px; /* Match price label width */
    margin-right: 10px;
    flex-shrink: 0;
     text-align: right;
  }

  .promo-content {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      gap: 8px; /* Increased gap */
      flex-grow: 1;
      line-height: 1.6; /* Improve readability */
  }

  .coupon-tag {
    border: 1px dotted $theme_color; /* Dotted border for coupons */
    color: $theme_color;
    background-color: #fff; /* White background */
    padding: 3px 8px; /* Adjust padding */
    border-radius: 2px;
    cursor: pointer;
    white-space: nowrap;
    font-size: 12px;
    &:hover {
       border-style: solid;
       background-color: lighten($theme_color, 48%);
    }
  }
  .more-coupons {
      color: #005ea7;
      cursor: pointer;
      font-size: 12px;
       &:hover {
           color: $theme_color;
           text-decoration: underline;
       }
  }

  .promo-tag {
      background-color: $theme_color;
      color: #fff;
      padding: 2px 6px;
      border-radius: 2px;
      margin-right: 5px;
      flex-shrink: 0;
      font-size: 12px;
      font-weight: bold;
  }
  .promo-desc {
      color: #333;
  }
}

/* SKU Selection Styles */
.sku-selection-section {
  margin-bottom: 20px; /* More space */
  border-top: 1px dotted #e5e5e5; /* Dotted border */
  padding-top: 20px;
  padding-left: 10px; /* Indent content */
}

.spec-group {
  display: flex;
  align-items: flex-start; 
  margin-bottom: 15px; /* More space between groups */
  line-height: 1.5;
}

.spec-name {
  color: #666;
  font-size: 13px;
  width: 60px; /* Match label width */
  margin-right: 10px;
  padding-top: 7px; /* Align better with buttons */
  flex-shrink: 0;
  text-align: right;
}

.spec-values {
  display: flex;
  flex-wrap: wrap;
  gap: 10px; /* Increased gap */
  flex-grow: 1;
}

.spec-value-item {
  border: 1px solid #bbb; /* Slightly darker border */
  padding: 6px 15px; /* More padding for button feel */
  cursor: pointer;
  border-radius: 3px;
  color: #333;
  background-color: #fff;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  font-size: 12px;
  min-width: 50px; /* Minimum width */
  text-align: center;

  &:hover {
    border-color: $theme_color;
    color: $theme_color;
  }

  &.active {
    border-color: $theme_color;
    color: $theme_color;
    background-color: lighten($theme_color, 48%);
    font-weight: bold;
    box-shadow: 0 0 0 1px $theme_color inset; /* Inner shadow effect */
  }

  &.disabled {
    cursor: not-allowed;
    color: #ccc;
    background-color: #f7f7f7;
    border-color: #eee;
     text-decoration: none; /* Remove line-through, maybe just grey out */
     &:hover {
       color: #ccc;
       border-color: #eee;
     }
  }
}

/* Weight display section style */
.weight-display-section {
   padding-left: 10px; /* Indent */
   margin-bottom: 10px; /* Space before purchase controls */
   .weight-info {
        font-size: 13px;
        color: #666;
        padding-top: 7px;
   }
}

/* Purchase Controls Area Styles */
.purchase-controls {
    display: flex;
    align-items: center; /* Vertically align items */
    margin-top: 20px; /* Space from SKU selection or weight */
    padding: 20px 10px; /* Add padding */
    border-top: 1px solid #f0f0f0; /* Top border for the whole area */
    background-color: #f9f9f9; /* Optional: Light background */
    border-radius: 4px;
    flex-wrap: wrap; /* Allow wrapping on smaller screens */
    gap: 15px; /* Gap between quantity, buttons, collect */
}

.quantity-selector {
    display: flex;
    align-items: center;
    flex-grow: 1; /* Allow quantity to take up space */
    min-width: 250px; /* Minimum width for quantity section */
     .spec-name {
         padding-top: 0; /* Remove padding top */
         width: 50px; /* Slightly narrower label */
     }
     .quantity-inputs {
         display: flex;
         align-items: center;
         gap: 8px;
     }
    .stock-info {
        font-size: 12px;
        color: #999;
        white-space: nowrap;
    }
    .wholesale-warning {
        font-size: 12px;
        color: $theme_color;
        margin-left: 5px;
        white-space: nowrap;
    }
}

.action-buttons-group {
    display: flex;
    align-items: center;
    flex-shrink: 0; /* Prevent buttons from shrinking too much */
    .ivu-btn {
      margin-right: 10px; /* Space between buttons */
      min-width: 130px; /* Adjust button width */
      padding: 10px 20px; /* Adjust button padding */
      font-size: 14px; /* Adjust button font size */
      font-weight: bold;
      border-radius: 3px;
       &:last-child {
         margin-right: 0; /* No margin for the last button */
       }
    }
    /* Specific button styles */
    .ivu-btn-warning.ivu-btn-ghost {
       color: #e47911;
       border-color: #e47911;
       &:hover {
         color: #fff;
         background-color: #e47911;
       }
    }
     .ivu-btn-error {
        background-color: $theme_color;
        border-color: $theme_color;
        &:hover {
           opacity: 0.9;
        }
     }
}

.collect-button-wrapper {
    margin-left: auto; /* Push collect button to the far right */
    flex-shrink: 0;
    padding-left: 15px; /* Space from action buttons */
}

.collect-button {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #666;
      font-size: 12px;
      min-width: 40px; 
      transition: color 0.2s ease;
      &:hover {
          color: $theme_color;
      }
      .ivu-icon {
          margin-bottom: 3px; 
      }
}

/* Service Commitment Styles */
.service-commitment {
    margin-top: 25px;
    padding: 15px 0 15px 10px;
    font-size: 12px;
    color: #999;
    border-top: 1px dotted #e5e5e5;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;

    span {
        margin-right: 0;
        white-space: nowrap;
        display: inline-flex;
        align-items: center;
    }
}

/* Deep selector for InputNumber */
/deep/ .ivu-input-number {
   width: 70px; /* Narrower input */
   vertical-align: middle;
   /* Adjust button size if needed */
   .ivu-input-number-handler-wrap {
     width: 20px;
   }
   .ivu-input-number-input {
     font-size: 13px;
     padding: 0 4px; /* Adjust padding */
   }
}

/* Remove default margin override for the new purchase controls */
/* .goods-info-container > div:not(:last-child) {
   margin-bottom: 15px; 
} */

/* Style for sticky placeholder */
.sticky-placeholder {
  width: 100%; /* Ensure it takes up space */
}

/* Styles for the sticky purchase controls */
.purchase-controls.is-sticky {
  position: fixed;
  bottom: 0; /* Stick to the bottom */
  left: auto; /* Let the parent sticky position handle left initially */
  /* Width is set dynamically via :style binding */
  z-index: 100; /* Ensure it's above other content */
  background-color: #fff; /* White background */
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.1); /* Shadow on top */
  /* Apply similar padding or define new ones */
  padding: 15px; /* Example padding */
  margin: 0; /* Remove margin when fixed */
  border-top: 1px solid #eee;
  /* Ensure alignment matches the parent sticky container if needed */
}

</style> 