<template>
  <div class="goods-images-container">
    <!-- 垂直缩略图列表 (移到左侧) -->
    <div class="thumbnail-list">
      <!-- 视频缩略图 (如果存在视频) -->
      <div
        v-if="videoUrl"
        class="thumbnail-item video-thumbnail"
        :class="{ active: showVideo }"
        @click="playVideo"
      >
        <img :src="thumbnailUrl || (imageList.length > 0 ? imageList[0] : '')" alt="视频缩略图" />
        <div class="thumbnail-play-icon">
          <Icon type="ios-play" size="16" color="#fff" /> <!-- Smaller icon -->
        </div>
      </div>
      <!-- 图片缩略图 -->
      <div
        v-for="(image, index) in imageList"
        :key="index"
        class="thumbnail-item image-thumbnail"
        :class="{ active: !showVideo && activeIndex === index }"
        @mouseover="showImage(index)"
      >
        <img :src="image.url || image" :alt="`商品图片 ${index + 1}`" />
      </div>
       <!-- 可以添加上下滚动箭头，如果缩略图过多 -->
    </div>

    <!-- 大图区域 (移到右侧) -->
    <div 
      class="main-image-area"
      @mouseenter="handleMainImageMouseEnter"
      @mouseleave="handleMainImageMouseLeave"
    >
      <template v-if="!showVideo && currentImageUrl">
        <pic-zoom
          v-if="isImageLargeEnoughForZoom && showZoomer"
          :url="currentImageUrl"
          :scale="2"
        ></pic-zoom>
        <img
          v-else
          :src="currentImageUrl"
          alt="商品主图"
          class="static-main-image"
        />
      </template>
      <!-- 视频播放器 -->
      <div v-if="showVideo && videoUrl" class="video-player-wrapper">
        <div :id="dplayerId" class="dplayer-instance"></div>
        <div class="video-close-button" @click="closeVideo">
          <Icon type="md-close" size="24" color="#fff" />
        </div>
      </div>
      <!-- 视频播放覆盖层 -->
       <div
        v-if="!showVideo && videoUrl && activeIndex === 0"
        class="video-overlay"
        @click="playVideo"
       >
        <div class="play-button-icon">
          <Icon type="ios-play" size="36" color="#fff" />
        </div>
      </div>
       <!-- 加载或无图提示 -->
       <div v-else-if="!currentImageUrl && !showVideo" class="no-image-placeholder">
         暂无图片
       </div>
    </div>

     <!-- 收藏按钮已移至 GoodsInfo -->
  </div>
</template>

<script>
 import PicZoom from "vue-piczoom";
import DPlayer from "dplayer";

export default {
  name: "GoodsImages",
  components: { PicZoom },
  props: {
    imageList: {
      type: Array,
      default: () => [],
    },
    videoUrl: {
      type: String,
      default: null,
    },
    thumbnailUrl: { 
       type: String,
       default: ''
    },
  },
  data() {
    return {
      dplayerId: 'dplayer-' + Math.random().toString(36).substr(2, 9),
      activeIndex: 0,
      showVideo: false,
      playerInstance: null,
      isImageLargeEnoughForZoom: false, // Default to false
      showZoomer: false // <-- New property
    };
  },
  computed: {
    currentImageUrl() {
        if (this.imageList && this.imageList.length > this.activeIndex && this.imageList[this.activeIndex]) {
            const item = this.imageList[this.activeIndex];
            return item.url || item;
        } 
        return null;
    },
    hasVideo() {
      return !!this.videoUrl;
    }
  },
  watch: {
    videoUrl(newVal, oldVal) {
      if (!newVal && oldVal) {
        this.closeVideo();
        this.showVideo = false;
      }
    },
    imageList() {
        this.activeIndex = 0;
        if(this.showVideo && !this.hasVideo) {
             this.closeVideo();
        }
        // imageList 变化也可能导致 currentImageUrl 变化, 触发其 watcher
    },
    currentImageUrl: {
      handler(newUrl) {
        if (newUrl) {
          const img = new Image();
          img.onload = () => {
            const minWidth = 100;
            const minHeight = 100;
            this.isImageLargeEnoughForZoom = img.naturalWidth >= minWidth && img.naturalHeight >= minHeight;
            if (!this.isImageLargeEnoughForZoom) {
              this.showZoomer = false; // Ensure zoomer is hidden if image not suitable
            }
          };
          img.onerror = () => {
            this.isImageLargeEnoughForZoom = false;
            this.showZoomer = false; // Ensure zoomer is hidden on error
          };
          img.src = newUrl;
        } else {
          this.isImageLargeEnoughForZoom = false;
          this.showZoomer = false; // Ensure zoomer is hidden if no URL
        }
      },
      immediate: true, // Call handler immediately when component is created
    }
  },
  mounted() {
    // REMOVED: console.log('[GoodsImages] Component mounted.');
  },
  methods: {
    showImage(index) {
      if (this.showVideo) {
        this.closeVideo();
        this.showVideo = false;
      }
      this.activeIndex = index;
    },
    handleMainImageMouseEnter() {
      // Only show zoomer if the image is large enough
      if (this.isImageLargeEnoughForZoom) {
        this.showZoomer = true;
      }
    },
    handleMainImageMouseLeave() {
      this.showZoomer = false;
    },
    playVideo() {
      if (!this.videoUrl) return;
      if(!this.showVideo){
         this.activeIndex = 0;
      }
      this.showVideo = true;
      this.$nextTick(() => {
        this.initializePlayer();
      }); 
    },
    closeVideo() {
      if (this.playerInstance) {
        this.playerInstance.pause();
        this.playerInstance.destroy();
        this.playerInstance = null;
      }
      this.showVideo = false;
    },
    initializePlayer() {
      if (!this.videoUrl) return;
      const container = document.getElementById(this.dplayerId);
      if (!container) {
          console.error(`DPlayer container not found: #${this.dplayerId}`);
          return;
      }
      if (!this.playerInstance) {
        this.playerInstance = new DPlayer({
          container: container,
          video: {
            url: this.videoUrl,
          },
          autoplay: true,
          theme: "#ed3f14",
          preload: "auto",
          volume: 0.7,
          contextmenu: [
            {
              text: "商品视频",
              link: "#",
            },
          ],
        });
      } else {
         if (this.playerInstance.video.currentSrc !== this.videoUrl) {
           this.playerInstance.switchVideo({ url: this.videoUrl });
         }
         this.playerInstance.play();
      }
    },
  },
  beforeDestroy() {
    if (this.playerInstance) {
      this.playerInstance.destroy();
      this.playerInstance = null;
    }
  },
};
</script>

<style scoped lang="scss">
.goods-images-container {
  display: flex;
  flex-direction: row;
  width: 100%; /* Fill the parent .left-scroll-container (720px) */
  gap: 10px;
}


/* Vertical Thumbnail List Styles */
.thumbnail-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 80px; /* Increased thumbnail width */
  flex-shrink: 0;
  max-height: 500px; /* Increase max-height as the container is wider now */
  overflow-y: auto;
}

.thumbnail-item {
  width: 80px; /* Increased thumbnail width */
  height: 80px; /* Increased thumbnail height */
  border: 1px solid #ddd;
  cursor: pointer;
  position: relative;
  overflow: hidden; /* Ensure content respects border-radius */
  background-color: #fff;
  flex-shrink: 0; /* Prevent items from shrinking */
  border-radius: 4px; /* Add border-radius */

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  &.active {
    border-color: $theme_color;
    box-shadow: 0 0 0 1px $theme_color; /* Add inset shadow effect */
  }

  &:hover {
     border-color: lighten($theme_color, 20%);
  }
}

.video-thumbnail {
  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
    transition: background-color 0.2s ease;
  }

  &:hover::before {
     background-color: rgba(0, 0, 0, 0.1);
  }

  &.active::before {
     background-color: rgba(0, 0, 0, 0);
  }
}

.thumbnail-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px; /* Smaller icon background */
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
  pointer-events: none;
}


.main-image-area {
  flex-grow: 1; 
  position: relative;
  overflow: hidden; 
  aspect-ratio:  1/ 1; 
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 6px; 
}

.static-main-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain; 
}

.video-player-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  z-index: 10;
}

.dplayer-instance {
  width: 100%;
  height: 100%;
}

.video-close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 15;

  &:hover {
    background-color: rgba(0, 0, 0, 0.7);
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  z-index: 5;
}

.play-button-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.8);
  }
}

.no-image-placeholder {
   color: #999;
   font-size: 16px;
}

</style> 

<style lang="scss">
body > canvas.mouse-cover-canvas {
  left: 1100px !important; /* Current value */
  top: 300px !important;   /* Adjusted:向上移动20px */
  background-color: #ffffff !important; /* Ensure it has a background */
}
</style>