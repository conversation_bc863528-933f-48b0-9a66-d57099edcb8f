<template>
  <div class="store-header-info">
    <div class="store-logo-container">
      <img :src="storeLogo" alt="Store Logo" class="store-logo" v-if="storeLogo" />
      <div class="logo-placeholder" v-else>店铺Logo</div>
    </div>
    <div class="store-details">
      <router-link :to="'/merchant?id=' + storeId" class="store-name">{{ storeName || '店铺名称加载中...' }}</router-link>
      <div class="store-ratings" v-if="false"> <!-- Hide ratings for now as data is missing -->
        <span>{{ storeRating }}</span>
        <span>{{ deliveryTime }}</span>
        <span>{{ responseTime }}</span>
        <span>{{ logisticsRating }}</span>
      </div>
    </div>
    <div class="store-actions">
      <Button type="primary" ghost size="small" @click="goToStore">进入店铺</Button>
      <Button type="default" size="small" @click="contactStore">联系客服</Button>
    </div>
  </div>
</template>

<script>
import imTalk from '@/components/mixes/talkIm';

export default {
  name: 'StoreHeaderInfo',
  mixins: [imTalk],
  props: {
    storeId: {
      type: [String, Number],
      required: true
    },
    storeName: {
      type: String,
      default: ''
    },
    storeLogo: {
      type: String,
      default: ''
    },
    // Ratings - Keep props defined but maybe not used until data is available
    storeRating: {
      type: [String, Number],
      default: 'N/A'
    },
    deliveryTime: {
      type: String,
      default: 'N/A'
    },
    responseTime: {
      type: String,
      default: 'N/A'
    },
    logisticsRating: {
      type: String,
      default: 'N/A'
    },
    // IDs needed for context, e.g., contacting support
    goodsId: {
       type: [String, Number]
    },
    skuId: {
        type: [String, Number]
    }
  },
  mounted() {
    // REMOVED: console.log('[StoreHeaderInfo] Mounted. Props received:');
    // REMOVED: console.log('[StoreHeaderInfo] storeId:', this.storeId);
    // REMOVED: console.log('[StoreHeaderInfo] storeName:', this.storeName);
    // REMOVED: console.log('[StoreHeaderInfo] storeLogo:', this.storeLogo);
  },
  methods: {
    goToStore() {
      this.$router.push({ path: '/merchant', query: { id: this.storeId } });
    },
    contactStore() {
        // Use the mixin method, passing necessary IDs
        if (this.storeId) {
             this.IMService(this.storeId, this.goodsId, this.skuId);
        } else {
            this.$Message.warning('无法联系客服，店铺ID缺失');
        }
    }
  }
}
</script>

<style scoped lang="scss">
.store-header-info {
  display: flex;
  align-items: center;
  padding: 15px 0; /* Adjust padding, remove horizontal padding if wrapper handles it */
  /* background-color: #f8f8f8; */ /* Removed background */
  /* border: 1px solid #eee; */ /* Removed border */
  border-radius: 4px;
  gap: 15px;
}

.store-logo-container {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #eee;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.store-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-placeholder {
  font-size: 12px;
  color: #999;
  text-align: center;
}

.store-details {
  flex-grow: 1;
  min-width: 0;
}

.store-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  display: block;
  &:hover {
    color: $theme_color;  
  }
}

.store-ratings {
  font-size: 12px;
  color: #999;
  display: flex;
  gap: 10px;
}

.store-actions {
  flex-shrink: 0;
  display: flex;
  gap: 10px;
  /* background-color: lightblue; */ /* Removed temporary background */
}
</style> 