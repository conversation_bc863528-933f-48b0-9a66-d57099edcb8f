// import {
//   getRequest,
//   postRequest,
//   putRequest,
//   deleteRequest,
//   importRequest,
//   getRequestWithNoToken
// } from '@/axios/index.js';
import request, {Method} from '@/plugins/request.js'

// 获取密码状态
export function getPwdStatus (params) {
  return request({
    url: '/buyer/passport/member/wallet/check',
    method: Method.GET,
    needToken: true,
    params
  })
}

// 设置密码
export function setPwd (params) {
  return request({
    url: '/buyer/passport/member/wallet/set-password',
    method: Method.POST,
    needToken: true,
    data: params
  })
}

// 设置支付密码
export function setUpdatePwdOrdinary (params) {
  return request({
    url: '/buyer/passport/member/wallet/update-password/ordinary',
    method: Method.GET,
    needToken: true,
    data: params
  })
}

// 修改会员资料
export function editMemberInfo (params) {
  return request({
    url: '/buyer/passport/member/editOwn',
    method: Method.PUT,
    needToken: true,
    data: params
  })
}

// 修改密码
export function editPwd (params) {
  return request({
    url: `/buyer/passport/member/modifyPass`,
    method: Method.PUT,
    needToken: true,
    data: params
  })
}

// 获取密码状态
export function logout () {
  return request({
    url: '/buyer/passport/member/logout',
    method: Method.POST,
    needToken: true
  })
}

/**
 * 验证短信验证码 (非登录场景)
 * @param scene 验证场景
 * @param mobile 手机号
 * @param code 短信验证码
 */
export function validateSmsCode (scene, mobile, code) {
  const requestBody = {
    mobile: mobile,
    code: code,
    verificationEnums: scene // 'scene' from argument corresponds to 'verificationEnums' in DTO
  };
  return request({
    url: '/buyer/passport/member/validate-sms-code', // New URL
    method: Method.POST, // New method
    needToken: true,
    headers: {
      'Content-Type': 'application/json' // Specify Content-Type for JSON body
    },
    data: requestBody // Pass constructed object as data (will be stringified by request.js)
  });
}

/**
 * 更新用户手机号
 * @param params 参数应包含: { mobile: newMobile, code: newMobileCode, password: (if required by backend) }
 *        The exact parameters depend on the backend API specification for '/member/update/phone'.
 */
export function updatePhoneNumber (params) {
  return request({
    url: '/buyer/passport/member/update-phone', // Corrected URL from user-provided image
    method: Method.PUT, // Corrected method from user-provided image
    needToken: true,
    headers: {
      'Content-Type': 'application/json' // Ensure Content-Type is set for JSON body
    },
    data: params
  });
}
