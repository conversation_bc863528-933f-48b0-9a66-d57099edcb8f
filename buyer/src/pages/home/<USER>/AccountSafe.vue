<template>
  <div class="wrapper">
    <card _Title="账户安全"/>
    <div class="safeList">
      <!-- 密码 -->
      <Row class="safeItem">
        <Col :span="2">
          <Icon size="40" type="md-key"/>
        </Col>
        <Col :span="16">
          <div class="setDivItem">登录密码</div>
          <div class="setDivItem theme">互联网账号存在被盗风险，建议您定期更改密码以保护账户安全。</div>
        </Col>
        <Col :span="4">
          <Button @click="modifyPwd">修改密码</Button>
        </Col>
      </Row>
      <!-- 手机号 -->
      <Row class="safeItem">
        <Col :span="2">
          <Icon size="40" type="md-phone-portrait"/>
        </Col>
        <Col :span="16">
          <div class="setDivItem">绑定手机</div>
          <div class="setDivItem theme">用于接收账户信息、验证身份及登录。请确保您的手机号畅通。</div>
        </Col>
        <Col :span="4">
          <Button @click="goToModifyPhoneNumber">修改手机</Button>
        </Col>
      </Row>
      <!-- 通过手机重设密码 -->
      <Row class="safeItem">
        <Col :span="2">
          <Icon size="40" type="md-unlock"/>
        </Col>
        <Col :span="16">
          <div class="setDivItem">通过手机重设密码</div>
          <div class="setDivItem theme">如果忘记旧密码，可以通过绑定的手机号验证身份并重设登录密码。</div>
        </Col>
        <Col :span="4">
          <Button @click="redirectToForgetPassword">重设密码</Button>
        </Col>
      </Row>
    </div>
  </div>
</template>

<script>
import { getPwdStatus } from '@/api/account';
export default {
  name: 'AccountSafe',
  data () {
    return {
      pwdStatus: '' // 密码状态
    }
  },
  mounted () {
    this.getPwdStatus()
  },
  methods: {
    // 设置支付密码
    goModifyPwd () {
      this.$router.push({name: 'ModifyPwd', query: { status: 2 }})
    },
    modifyPwd () { // 修改密码
      this.$router.push({name: 'ModifyPwd', query: { status: 1 }})
    },
    goToModifyPhoneNumber () {
      this.$router.push({name: 'ModifyPhoneNumber'})
    },
    redirectToForgetPassword() { // 新增方法，导航到忘记密码页面
      this.$router.push({ name: 'forgetPassword' });
    },
    // 获取密码状态
    getPwdStatus () {
      getPwdStatus().then(res => {
        if (res) {
          this.pwdStatus = '修改密码'
        } else {
          this.pwdStatus = '设置密码'
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
  /deep/ .ivu-col-span-2, .ivu-col-span-4 {
    text-align: center;
    color: $theme_color;
  }

  .theme {
    color: $theme_color;
  }

  .setDivItem {
    line-height: 1.75;
  }

  .safeItem {
    border-bottom: 1px solid $border_color;
    padding: 16px 0;

    /deep/ .ivu-col {
      padding: 8px 0;

    }
  }
</style>
