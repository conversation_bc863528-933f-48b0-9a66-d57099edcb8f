<template>
  <div class="company-msg">
    <Form ref="firstForm" :model="form" :rules="rules" :label-width="140">
      <h4>基础信息</h4>
      <FormItem prop="companyName" label="公司名称">
        <Input
          type="text"
          v-model="form.companyName"
          placeholder="请填写公司信息"
        />
      </FormItem>
      <FormItem prop="companyAddressIdPath" label="公司所在地">
        <span>{{ form.companyAddressPath || '暂无地址' }}</span>
        <Button type="default" style="margin-left: 10px;" @click="$refs.map.open()">选择</Button>
      </FormItem>
      <FormItem prop="companyAddress" label="公司详细地址">
        <Input
          type="text"
          v-model="form.companyAddress"
          placeholder="请填写公司详细信息"
        />
      </FormItem>
      <FormItem prop="employeeNum" label="员工总数">
        <Input
          type="text"
          v-model="form.employeeNum"
          placeholder="请填写公司员工总数"
          ><span slot="append">人</span>
          </Input>
      </FormItem>
      <FormItem prop="companyPhone" label="公司电话">
        <Input
          type="text"
          v-model="form.companyPhone"
          placeholder="请填写公司电话"
          ></Input>
      </FormItem>
      <FormItem prop="registeredCapital" label="注册资金">
        <Input
          type="text"
          v-model="form.registeredCapital"
          placeholder="请填写注册资金"
          ><span slot="append">万元</span></Input>
      </FormItem>
      <FormItem prop="linkName" label="联系人姓名">
        <Input
          type="text"
          v-model="form.linkName"
          placeholder="请填写联系人姓名"
        />
      </FormItem>
      <FormItem prop="linkPhone" label="联系人电话">
        <Input
          type="text"
          v-model="form.linkPhone"
          placeholder="请填写联系人电话"
        />
      </FormItem>
      <FormItem prop="companyEmail" label="电子邮箱">
        <Input
          type="text"
          v-model="form.companyEmail"
          placeholder="请填写电子邮箱"
        />
      </FormItem>

      <h4>营业执照信息</h4>
      <FormItem prop="licenseNum" label="营业执照号">
        <Input
          type="text"
          v-model="form.licenseNum"
          placeholder="请填写营业执照号"
        />
      </FormItem>
      <FormItem prop="scope" label="法定经营范围">
        <Input
          type="textarea"
          v-model="form.scope"
          maxlength="200"
          show-word-limit
          :rows="4"
          placeholder="请输入营业执照所示经营范围"
        />
      </FormItem>
      <FormItem prop="licencePhoto" label="营业执照电子版">
        <Upload
          ref="uploadLicence"
          :show-upload-list="false"
          :on-success="handleSuccess"
          :format="['jpg', 'jpeg', 'png', 'gif']"
          :max-size="2048"
          :before-upload="beforeUpload"
          :on-format-error="handleFormatError"
          :on-exceeded-size="handleMaxSize"
          :on-error="uploadErr"
          multiple
          :headers="accessToken"
          :action="action"
        >
          <Button type="info" :loading="uploadLoading">证照上传</Button>
        </Upload>
        <div class="describe">
          请压缩图片在2M以内，格式为gif，jpg，png，并确保文字清晰，以免上传或审核失败
        </div>
        <div
          class="img-list"
          v-for="(item, index) in form.licencePhoto"
          :key="index"
        >
          <img :src="item" width="100" alt="" />
          <div class="cover">
            <Icon
              type="ios-eye-outline"
              @click.native="handleView(item)"
            ></Icon>
            <Icon
              type="ios-trash-outline"
              @click.native="handleRemove(index, 'licencePhoto')"
            ></Icon>
          </div>
        </div>
      </FormItem>

      <h4>法人信息</h4>
      <FormItem prop="legalName" label="法人姓名">
        <Input
          type="text"
          v-model="form.legalName"
          maxlength="20"
          placeholder="请输入法人姓名"
        />
      </FormItem>
      <FormItem prop="legalId" label="法人证件号">
        <Input
          type="text"
          v-model="form.legalId"
          placeholder="请输入法人证件号"
        />
      </FormItem>
      <FormItem prop="legalBirthDate" label="出生日期">
        <DatePicker
          type="date"
          v-model="form.legalBirthDate"
          format="yyyy-MM-dd"
          placeholder="由身份证自动识别"
          readonly
          style="width: 300px"
        ></DatePicker>
      </FormItem>
      <FormItem prop="legalIdValidPeriod" label="身份证有效期">
        <div style="display: flex; align-items: center; width: 300px">
          <DatePicker
            type="date"
            v-model="form.legalIdValidFrom"
            format="yyyy-MM-dd"
            placeholder="有效期起始日"
            readonly
            style="width: 140px; margin-right: 10px"
          ></DatePicker>
          <span style="margin: 0 5px;">至</span>
          <Input
            v-model="form.legalIdValidTo"
            placeholder="有效期截止日/长期"
            readonly
            style="width: 140px"
          ></Input>
        </div>
      </FormItem>
      <FormItem prop="legalPhoto" label="法人证件电子版">
        <div style="display: flex; flex-direction: column; gap: 10px;">
          <div>
        <Upload
              ref="uploadLegalFront"
          :show-upload-list="false"
              :on-success="handleSuccessFront"
              :before-upload="beforeUploadFront"
          :max-size="2048"
          :format="['jpg', 'jpeg', 'png', 'gif']"
          :on-format-error="handleFormatError"
          :on-exceeded-size="handleMaxSize"
          :on-error="uploadErr"
              :headers="accessToken"
          :action="action"
            >
              <Button type="info" :loading="uploadLoadingFront">上传身份证正面</Button>
            </Upload>
            <span class="describe" style="margin-left: 10px;">包含姓名、身份证号、出生日期等信息</span>
          </div>
          <div>
            <Upload
              ref="uploadLegalBack"
              :show-upload-list="false"
              :on-success="handleSuccessBack"
              :before-upload="beforeUploadBack"
              :max-size="2048"
              :format="['jpg', 'jpeg', 'png', 'gif']"
              :on-format-error="handleFormatError"
              :on-exceeded-size="handleMaxSize"
              :on-error="uploadErr"
          :headers="accessToken"
              :action="action"
        >
              <Button type="info" :loading="uploadLoadingBack">上传身份证反面</Button>
        </Upload>
            <span class="describe" style="margin-left: 10px;">包含签发机关、有效期等信息</span>
          </div>
        </div>
        <div class="describe" style="margin-top: 10px;">
          请压缩图片在2M以内，格式为gif，jpg，png，并确保图片清晰无缺角
        </div>
        <div
          class="img-list"
          v-for="(item, index) in form.legalPhoto"
          :key="index"
        >
          <img :src="item" width="100" alt="" />
          <div class="cover">
            <Icon
              type="ios-eye-outline"
              @click.native="handleView(item)"
            ></Icon>
            <Icon
              type="ios-trash-outline"
              @click.native="handleRemove(index, 'legalPhoto')"
            ></Icon>
          </div>
        </div>
      </FormItem>
      <FormItem>
        <Button type="primary" :loading="loading" @click="next"
          >填写财务资质信息</Button
        >
      </FormItem>
    </Form>
    <Modal title="View Image" v-model="visible">
      <img :src="previewPicture" v-if="visible" style="width: 100%" />
    </Modal>

    <multipleMap ref="map" @callback="getAddress" />
  </div>
</template>
<script>
import { applyFirst } from '@/api/shopentry';
import * as RegExp from '@/plugins/RegExp.js';
import multipleMap from "@/components/map/multiple-map";
import storage from '@/plugins/storage';
import { commonUrl, Method } from '@/plugins/request.js';
import request from '@/plugins/request.js';
import { recognizeBusinessLicense, recognizeIDCardFront, recognizeIDCardBack } from '@/api/common';

export default {
  components: { multipleMap },
  props: {
    content: {
      default: {},
      type: Object
    }
  },
  data () {
    return {
      action: commonUrl + '/common/common/upload/file', // 上传地址
      accessToken: {}, // 验证token
      visible: false, // 预览图片
      loading: false, // 加载状态

      previewPicture: '', // 预览图片url
      form: { // 表单数据
        legalPhoto: [],
        licencePhoto: [],
        legalBirthDate: '',
        legalIdValidFrom: '',
        legalIdValidTo: ''
      },
      rules: { // 验证规则
        companyName: [{ required: true, message: '请填写公司信息' }],
        companyAddressIdPath: [{ required: true, message: '请选择公司所在地' }],
        companyAddress: [{ required: true, message: '请填写公司详细地址' }],
        employeeNum: [
          { required: true, message: '请填写公司员工总数' },
          { pattern: RegExp.integer, message: '只能填写正整数' }
        ],
        registeredCapital: [
          { required: true, message: '请填写公司注册资金' },
          { pattern: RegExp.integer, message: '只能填写正整数' }
        ],
        linkName: [{ required: true, message: '请填写联系人姓名' }],
        linkPhone: [
          { required: true, message: '请填写联系人电话' },
          { pattern: RegExp.mobile, message: '请填写正确的号码' }
        ],
        companyPhone: [
          { required: true, message: '请填写公司电话' },
          { pattern: RegExp.mobile, message: '请填写正确的号码' }
        ],
        companyEmail: [
          { required: true, message: '请填写电子邮箱' },
          { type: 'email', message: '请输入正确的邮箱' }
        ],
        licenseNum: [
          { required: true, message: '请填写营业执照号' },
          { pattern: RegExp.licenseNum, message: '请输入正确的营业执照号' }
        ],
        scope: [{ required: true, message: '请填写营业执照所示经营范围' }],
        legalPhoto: [{ required: true, message: '请上传法人身份证照片' }],
        licencePhoto: [{ required: true, message: '请上传营业执照' }],
        legalName: [{ required: true, message: '请输入法人姓名' }],
        legalId: [
          { required: true, message: '请输入法人证件号' },
          { pattern: RegExp.IDCard, message: '请输入正确的证件号' }
        ]
      },
      uploadLoading1: false, // 上传loading
      uploadLoading: false, // 上传loading
      uploadLoadingFront: false,
      uploadLoadingBack: false
    };
  },
  methods: {
    // 将不同格式的日期字符串转换为标准格式
    formatDateString(dateStr) {
      if (!dateStr) return null;
      
      // 移除可能存在的空格
      dateStr = dateStr.trim();
      
      // 处理 "年月日" 格式，如 "2016年02月15日"
      if (dateStr.includes('年')) {
        return dateStr
          .replace('年', '-')
          .replace('月', '-')
          .replace('日', '');
      }
      
      // 处理 "YYYY.MM.DD" 格式
      if (dateStr.includes('.')) {
        return dateStr.replace(/\./g, '-');
      }
      
      return dateStr;
    },

    // 获取店铺地址
    getAddress(val){
      if(val.type === 'select'){
        const paths = val.data.map(item => item.name).join(',')
        const ids = val.data.map(item => item.id).join(',')
        this.$set(this.form, 'companyAddressIdPath', ids)
        this.$set(this.form, 'companyAddressPath', paths)
      }else{
        this.$set(this.form, 'companyAddressIdPath', val.data.addrId)
        this.$set(this.form, 'companyAddressPath', val.data.addr)
      }
    },

    // 下一步
    next () {
      this.$refs.firstForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          let params = JSON.parse(JSON.stringify(this.form));
          params.legalPhoto = this.form.legalPhoto.toString();
          params.licencePhoto = this.form.licencePhoto.toString();
          applyFirst(params)
            .then((res) => {
              this.loading = false;
              if (res.success) this.$emit('change', 1);
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          console.log('error');
        }
      });
    },

    // 营业执照上传前
    beforeUpload (file) {
      // Start loading state
      this.uploadLoading = true;

      // Call OCR API
      recognizeBusinessLicense(file)
        .then(res => {
          if (res.code === 200 && res.result) {
            try {
              const ocrData = res.result;

              const allFieldsEmpty = !ocrData || Object.values(ocrData).every(value => value === null || value === '');
              
              if (allFieldsEmpty) {
                this.$Message.warning('OCR未能识别出营业执照信息，请手动填写表单');
                return this.uploadFileGeneric(file);
              }
              
              let fieldsUpdated = false;
              if (ocrData.companyName) {
                this.$set(this.form, 'companyName', ocrData.companyName);
                fieldsUpdated = true;
              }
              if (ocrData.licenseNum) {
                this.$set(this.form, 'licenseNum', ocrData.licenseNum);
                fieldsUpdated = true;
              }
              if (ocrData.legalPerson) {
                this.$set(this.form, 'legalName', ocrData.legalPerson);
                fieldsUpdated = true;
              }
              if (ocrData.address) {
                this.$set(this.form, 'companyAddress', ocrData.address);
                fieldsUpdated = true;
              }
              if (ocrData.registeredCapital) {
                this.$set(this.form, 'registeredCapital', ocrData.registeredCapital);
                fieldsUpdated = true;
              }
              if (ocrData.businessScope) {
                this.$set(this.form, 'scope', ocrData.businessScope);
                fieldsUpdated = true;
              }
              
              if (fieldsUpdated) {
                this.$Message.success('营业执照信息识别成功，请核对并补充');
              } else {
                this.$Message.warning('OCR识别结果不完整，请手动补充表单');
              }
            } catch (err) {
              this.$Message.warning('OCR数据处理出错，请手动填写表单');
              return this.uploadFileGeneric(file);
            }
            
            return this.uploadFileGeneric(file);
          } else {
            this.$Message.error(res.message || '营业执照信息识别失败');
            this.uploadLoading = false;
            return this.uploadFileGeneric(file);
          }
        })
        .then(imageUrl => {
          if (imageUrl) {
             this.form.licencePhoto.push(imageUrl);
          }
        })
        .catch(error => {
          this.uploadLoading = false;
        })
        .finally(() => {
          this.uploadLoading = false;
        });

      return false;
    },
    // 法人证件上传前
    beforeUploadFront (file) {
      this.uploadLoadingFront = true;

      recognizeIDCardFront(file)
        .then(res => {
          if (res.code === 200 && res.result) {
            try {
              const ocrData = res.result;
              
              if (ocrData.name) {
                this.$set(this.form, 'legalName', ocrData.name);
              }
              if (ocrData.idNumber) {
                this.$set(this.form, 'legalId', ocrData.idNumber);
              }
              if (ocrData.birthDate) {
                this.$set(this.form, 'legalBirthDate', new Date(this.formatDateString(ocrData.birthDate))); // Ensure date is formatted if needed
              }
              
              this.$Message.success('身份证正面信息识别成功，正在上传图片...');

              this.uploadFileGeneric(file)
                .then(imageUrl => {
                  if (imageUrl) {
                    this.form.legalPhoto.push(imageUrl);
                    this.$Message.success('身份证正面图片上传成功');
                  }
                })
                .catch(uploadError => {
                })
                .finally(() => {
                  this.uploadLoadingFront = false;
                });

            } catch (err) {
              this.$Message.warning('身份证信息处理出错，请手动填写');
              this.uploadLoadingFront = false;
            }
          } else {
            this.$Message.error(res.message || '身份证正面信息识别失败，图片未上传');
            this.uploadLoadingFront = false;
          }
        })
        .catch(error => {
          this.$Message.error('身份证正面识别服务异常，图片未上传');
          this.uploadLoadingFront = false;
        });

      return false;
    },
    beforeUploadBack (file) {
      this.uploadLoadingBack = true;

      recognizeIDCardBack(file)
        .then(res => {
          if (res.code === 200 && res.result) {
            try {
              const ocrData = res.result;
              
              if (ocrData.issueAuthority) {
                // console.log('签发机关:', ocrData.issueAuthority); // REMOVED - this was a specific non-error log
              }
              
              if (ocrData.validFrom) {
                const validFromStr = this.formatDateString(ocrData.validFrom);
                if (validFromStr) {
                  this.$set(this.form, 'legalIdValidFrom', new Date(validFromStr));
                }
              }
              if (ocrData.validTo) {
                  const validToStr = ocrData.validTo.toString().trim().toLowerCase() === '长期' ? '长期' : this.formatDateString(ocrData.validTo);
                   if (validToStr === '长期') {
                    this.$set(this.form, 'legalIdValidTo', '长期');
                  } else if (validToStr) {
                    this.$set(this.form, 'legalIdValidTo', validToStr); 
                  }
              }
              
              this.$Message.success('身份证反面信息识别成功，正在上传图片...');

              this.uploadFileGeneric(file)
                .then(imageUrl => {
                  if (imageUrl) {
                    this.form.legalPhoto.push(imageUrl);
                    this.$Message.success('身份证反面图片上传成功');
                  }
                })
                .catch(uploadError => {
                  // console.error('身份证反面图片上传失败（OCR成功后）:', uploadError); // REMOVED
                })
                .finally(() => {
                  this.uploadLoadingBack = false;
                });

            } catch (err) {
              // console.error('身份证反面OCR数据处理错误:', err); // REMOVED
              this.$Message.warning('身份证反面信息处理出错，请手动填写');
              this.uploadLoadingBack = false;
            }
          } else {
            // console.error('身份证反面OCR识别失败:', JSON.stringify(res)); // REMOVED
            this.$Message.error(res.message || '身份证反面信息识别失败，图片未上传');
            this.uploadLoadingBack = false;
          }
        })
        .catch(error => {
          // console.error('身份证反面处理错误:', error); // REMOVED
          this.$Message.error('身份证反面识别服务异常，图片未上传');
          this.uploadLoadingBack = false;
        });

      return false;
    },
    // 上传成功回调
    handleSuccess (res, file) {
      this.uploadLoading = false;
      // 仅在直接上传时处理，OCR流程通过beforeUpload和uploadFileGeneric处理
      if (res && res.success && res.result) {
      this.form.licencePhoto.push(res.result);
      }
    },
    handleSuccessFront (res, file) {
      this.uploadLoadingFront = false;
      // this.form.legalPhoto.push(res.result); // Logic moved to beforeUploadFront
    },
    handleSuccessBack (res, file) {
      this.uploadLoadingBack = false;
      // this.form.legalPhoto.push(res.result); // Logic moved to beforeUploadBack
    },
    // 上传失败
    uploadErr () {
      this.uploadLoading = false;
      this.uploadLoadingFront = false;
      this.uploadLoadingBack = false;
    },
    // 上传失败回调
    handleFormatError (file) {
      this.uploadLoading = false;
      this.uploadLoadingFront = false;
      this.uploadLoadingBack = false;
      this.$Notice.warning({
        title: 'The file format is incorrect',
        desc: '上传文件格式不正确'
      });
    },
    // 上传大小限制
    handleMaxSize (file) {
      this.uploadLoading = false;
      this.uploadLoadingFront = false;
      this.uploadLoadingBack = false;
      this.$Notice.warning({
        title: 'Exceeding file size limit',
        desc: '文件大小不能超过2M'
      });
    },
    // 图片查看
    handleView (item) {
      this.previewPicture = item;
      this.visible = true;
    },
    // 删除图片
    handleRemove (index, listName) {
      this.form[listName].splice(index, 1);
    },
    // Generic file upload method (adapted from existing logic)
    async uploadFileGeneric(file) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const res = await request({
          url: commonUrl + '/common/common/upload/file',
          method: Method.POST,
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
             'accessToken': this.accessToken.accessToken
          }
        });

        if (!res) {
          // console.error('上传响应为空'); // REMOVED
          this.$Message.error('上传失败: 服务器响应为空');
          return null;
        }

        // console.log('上传响应:', JSON.stringify(res)); // REMOVED

        if (res.success) {
          return res.result;
        } else {
          const errorMsg = res.message && res.message !== 'success' ? res.message : '图片上传失败';
          this.$Message.error(errorMsg);
          return null;
        }
      } catch (error) {
        // console.error('文件上传错误:', error); // REMOVED
        this.$Message.error('图片上传失败');
        return null;
      }
    },
  },
  mounted () {
    this.accessToken.accessToken = storage.getItem('accessToken');
    if (Object.keys(this.content).length) { // 处理回显数据
      this.form = JSON.parse(JSON.stringify(this.content));
      if (this.form.licencePhoto) {
        this.form.legalPhoto = this.content.legalPhoto.split(',');
        this.form.licencePhoto = this.content.licencePhoto.split(',');

      }
    }
  }
};
</script>
<style lang="scss" scoped>
h4 {
  margin-bottom: 10px;
  padding: 0 10px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  line-height: 40px;
  text-align: left;
}
.ivu-input-wrapper {
  width: 300px;
}
.img-list {
  display: inline-block;
  margin: 10px;
  width: 100px;
  position: relative;
  .cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    width: inherit;
    height: inherit;
    align-items: center;
    justify-content: space-around;
    i {
      color: #fff;
      font-size: 30px;
      cursor: pointer;
    }
  }
  &:hover .cover {
    display: flex;
  }
}
.describe {
  font-size: 12px;
  color: #999;
}
</style>
